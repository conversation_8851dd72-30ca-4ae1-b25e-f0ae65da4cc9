<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">在线答题页面显示测试</text>
    </view>
    
    <!-- 测试ExamExecution组件 -->
    <view class="test-section">
      <text class="section-title">测试题目显示效果</text>
      
      <!-- 模拟长题目文本 -->
      <view class="mock-question">
        <view class="question-text">
          <u-text 
            text="这是一个非常长的题目文本，用来测试文本换行功能是否正常工作。题目内容包含了很多文字，需要确保在不同屏幕尺寸下都能正确换行显示，不会出现内容重叠的问题。这个题目还包含了一些专业术语和复杂的表述，比如：疾病预防控制中心（CDC）的主要职责是什么？"
            size="14"
            color="#303133"
            line-height="1.6"
            wordWrap="break-word"
            :custom-style="{ 
              wordWrap: 'break-word', 
              wordBreak: 'break-all', 
              whiteSpace: 'pre-wrap',
              display: 'block',
              width: '100%'
            }"
          />
        </view>
        
        <!-- 模拟选项 -->
        <view class="options-list">
          <view class="option-item">
            <view class="option-key">A</view>
            <view class="option-text">
              <u-text 
                text="负责疾病监测、预防控制、应急处置、健康教育等公共卫生工作，这是一个很长的选项文本用来测试换行效果"
                size="13"
                color="#303133"
                line-height="1.5"
                wordWrap="break-word"
                :custom-style="{ 
                  wordWrap: 'break-word', 
                  wordBreak: 'break-all', 
                  whiteSpace: 'pre-wrap',
                  display: 'block',
                  width: '100%'
                }"
              />
            </view>
          </view>
          
          <view class="option-item">
            <view class="option-key">B</view>
            <view class="option-text">
              <u-text 
                text="仅负责疾病治疗工作，这个选项相对较短"
                size="13"
                color="#303133"
                line-height="1.5"
                wordWrap="break-word"
                :custom-style="{ 
                  wordWrap: 'break-word', 
                  wordBreak: 'break-all', 
                  whiteSpace: 'pre-wrap',
                  display: 'block',
                  width: '100%'
                }"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 测试监控提醒区域 -->
    <view class="test-section">
      <text class="section-title">测试监控提醒区域</text>
      
      <view class="exam-warning-multiline">
        <view class="warning-header">
          <u-icon 
            name="error-circle-fill" 
            color="#e6a23c" 
            size="16"
          />
          <u-text 
            text="考试监控提醒"
            color="#8b6914"
            size="12"
            bold
            margin="0 0 0 6rpx"
          />
        </view>
        <view class="warning-text">
          <u-text 
            text="请勿切换应用或离开考试页面，考试过程已启用防作弊监控。系统将记录您的操作行为，确保考试公平性。"
            color="#8b6914"
            size="11"
            line-height="1.5"
            wordWrap="break-word"
            :custom-style="{ 
              wordWrap: 'break-word', 
              wordBreak: 'break-all', 
              whiteSpace: 'pre-wrap',
              display: 'block',
              width: '100%'
            }"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 测试页面，无需复杂逻辑
</script>

<style lang="scss" scoped>
.test-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .test-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.test-section {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 16rpx;
    display: block;
  }
}

.mock-question {
  .question-text {
    margin-bottom: 16rpx;
    word-wrap: break-word;
    word-break: break-all;
    white-space: pre-wrap;
    line-height: 1.6;
    overflow-wrap: break-word;
    hyphens: auto;
    
    :deep(.u-text__value) {
      word-wrap: break-word !important;
      word-break: break-all !important;
      white-space: pre-wrap !important;
      overflow-wrap: break-word !important;
      display: block !important;
      width: 100% !important;
    }
  }
  
  .options-list {
    .option-item {
      display: flex;
      align-items: flex-start;
      background: #f8faff;
      border: 2rpx solid transparent;
      border-radius: 8rpx;
      padding: 10rpx;
      margin-bottom: 8rpx;
      
      .option-key {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        background: #ffffff;
        border: 2rpx solid #e4e7ed;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 22rpx;
        font-weight: 600;
        color: #606266;
        margin-right: 12rpx;
        flex-shrink: 0;
      }
      
      .option-text {
        flex: 1;
        padding-top: 2rpx;
        word-wrap: break-word;
        word-break: break-all;
        white-space: pre-wrap;
        line-height: 1.5;
        overflow-wrap: break-word;
        hyphens: auto;
        
        :deep(.u-text__value) {
          word-wrap: break-word !important;
          word-break: break-all !important;
          white-space: pre-wrap !important;
          overflow-wrap: break-word !important;
          display: block !important;
          width: 100% !important;
        }
      }
    }
  }
}

// 新的多行考试监控提醒样式
.exam-warning-multiline {
  padding: 12rpx 16rpx;
  background: linear-gradient(135deg, #fffbe6 0%, #fdf8e0 100%);
  border: 1rpx solid rgba(230, 162, 60, 0.3);
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(230, 162, 60, 0.12);
  min-height: 60rpx;

  .warning-header {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;
  }

  .warning-text {
    padding-left: 22rpx;
    word-wrap: break-word;
    word-break: break-all;
    white-space: pre-wrap;
    line-height: 1.5;
  }
}
</style>
