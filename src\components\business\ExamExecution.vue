<template>
  <view class="exam-execution-container">
    <!-- 固定头部 - 考试进度和计时器 -->
    <view class="exam-header-fixed">
      <view class="exam-progress-section">
        <u-text 
          :text="`第 ${currentQuestionIndex + 1} 题 / 共 ${questions.length} 题`"
          size="12"
          color="#606266"
          margin="0 0 4rpx 0"
        />
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            :style="{ width: progressPercent + '%' }"
          />
        </view>
      </view>
      
      <view class="exam-timer">
        <u-icon
          name="clock"
          size="14"
          color="#ff6b35"
        />
        <u-text 
          :text="formatTime(remainingTime)"
          size="12"
          color="#ff6b35"
          bold
          margin="0 0 0 4rpx"
        />
      </view>
    </view>

    <!-- 可滚动内容区域 - 题目内容 -->
    <scroll-view 
      class="exam-content-scroll" 
      scroll-y 
      enhanced 
      :show-scrollbar="false"
    >
      <view class="question-container">
        <u-card
          v-if="currentQuestion"
          :border="false"
          background="#ffffff"
          border-radius="8rpx"
          margin="0"
        >
          <template #body>
            <view class="question-content">
              <view class="question-header">
                <u-tag 
                  :text="getQuestionTypeText(currentQuestion.type)"
                  type="primary"
                  size="small"
                  shape="circle"
                />
              </view>
              
              <view class="question-text">
                <u-text
                  :text="currentQuestion.stem"
                  size="14"
                  color="#303133"
                  line-height="1.6"
                  wordWrap="break-word"
                  :custom-style="{
                    wordWrap: 'break-word',
                    wordBreak: 'break-all',
                    whiteSpace: 'pre-wrap',
                    display: 'block',
                    width: '100%'
                  }"
                />
              </view>
          
              <!-- 题目图片 -->
              <u-image
                v-if="currentQuestion.image" 
                :src="currentQuestion.image"
                width="100%"
                height="auto"
                mode="widthFix"
                border-radius="6rpx"
                margin="8rpx 0"
                @click="previewImage(currentQuestion.image)"
              />
          
              <!-- 选项列表 -->
              <view class="options-list">
                <!-- 判断题特殊处理 -->
                <template v-if="currentQuestion.type === 'judgment' || currentQuestion.type === 'judge'">
                  <view 
                    class="option-item"
                    :class="{ selected: selectedAnswers.includes('true') }"
                    @tap="handleSelectOption('true')"
                  >
                    <view class="option-key">A</view>
                    <view class="option-text">
                      <u-text
                        text="正确"
                        size="13"
                        color="#303133"
                        line-height="1.5"
                        wordWrap="break-word"
                        :custom-style="{
                          wordWrap: 'break-word',
                          wordBreak: 'break-all',
                          whiteSpace: 'pre-wrap',
                          display: 'block',
                          width: '100%'
                        }"
                      />
                    </view>
                  </view>
                  <view
                    class="option-item"
                    :class="{ selected: selectedAnswers.includes('false') }"
                    @tap="handleSelectOption('false')"
                  >
                    <view class="option-key">B</view>
                    <view class="option-text">
                      <u-text
                        text="错误"
                        size="13"
                        color="#303133"
                        line-height="1.5"
                        wordWrap="break-word"
                        :custom-style="{
                          wordWrap: 'break-word',
                          wordBreak: 'break-all',
                          whiteSpace: 'pre-wrap',
                          display: 'block',
                          width: '100%'
                        }"
                      />
                    </view>
                  </view>
                </template>
            
                <!-- 问答题特殊处理 -->
                <template v-else-if="currentQuestion.type === 'essay'">
                  <view class="essay-input-container">
                    <u-textarea
                      v-model="essayAnswer"
                      placeholder="请在此输入您的答案..."
                      :maxlength="1000"
                      :show-confirm-bar="false"
                      :auto-height="true"
                      :height="160"
                      border-radius="6rpx"
                      @input="handleEssayInput"
                    />
                    <view class="char-count">
                      <u-text 
                        :text="`${essayAnswer.length}/1000`"
                        size="10"
                        color="#909399"
                        wordWrap="break-word"
                      />
                    </view>
                  </view>
                </template>
            
                <!-- 选择题正常处理 -->
                <template v-else>
                  <view 
                    v-for="(option, index) in currentQuestion.options" 
                    :key="option.key || index"
                    class="option-item"
                    :class="{ selected: selectedAnswers.includes(option.key || String.fromCharCode(65 + index)) }"
                    @tap="handleSelectOption(option.key || String.fromCharCode(65 + index))"
                  >
                    <view class="option-key">
                      {{ option.key || String.fromCharCode(65 + index) }}
                    </view>
                    <view class="option-text">
                      <u-text
                        :text="getOptionText(option)"
                        size="13"
                        color="#303133"
                        line-height="1.5"
                        wordWrap="break-word"
                        :custom-style="{
                          wordWrap: 'break-word',
                          wordBreak: 'break-all',
                          whiteSpace: 'pre-wrap',
                          display: 'block',
                          width: '100%'
                        }"
                      />
                    </view>
                  </view>
                </template>
              </view>
            </view>
          </template>
        </u-card>
      </view>
    </scroll-view>

    <!-- 固定底部 - 操作按钮和监控提醒 -->
    <view class="exam-footer-fixed">
      <!-- 操作按钮 -->
      <view class="answer-actions">
        <u-button 
          v-if="currentQuestionIndex > 0"
          text="上一题"
          type="info"
          size="normal"
          shape="round"
          :custom-style="{ flex: 1, marginRight: '12rpx' }"
          @click="handlePrevQuestion"
        />
        
        <u-button 
          v-if="currentQuestionIndex < questions.length - 1"
          text="下一题"
          type="primary"
          size="normal"
          shape="round"
          :custom-style="{ flex: 1 }"
          @click="handleNextQuestion"
        />
        
        <u-button 
          v-else
          text="提交试卷"
          type="success"
          size="normal"
          shape="round"
          :custom-style="{ flex: 1 }"
          @click="handleShowSubmitConfirm"
        />
      </view>

      <!-- 考试监控提醒 -->
      <view class="exam-warning-multiline">
        <view class="warning-header">
          <u-icon
            name="error-circle-fill"
            color="#e6a23c"
            size="16"
          />
          <u-text
            text="考试监控提醒"
            color="#8b6914"
            size="12"
            bold
            margin="0 0 0 6rpx"
          />
        </view>
        <view class="warning-text">
          <u-text
            text="请勿切换应用或离开考试页面，考试过程已启用防作弊监控。系统将记录您的操作行为，确保考试公平性。"
            color="#8b6914"
            size="11"
            line-height="1.5"
            wordWrap="break-word"
            :custom-style="{
              wordWrap: 'break-word',
              wordBreak: 'break-all',
              whiteSpace: 'pre-wrap',
              display: 'block',
              width: '100%'
            }"
          />
        </view>
      </view>
    </view>

    <!-- 提交确认弹窗 -->
    <u-modal
      v-model="showSubmitModal"
      title="确认提交"
      :content="submitModalContent"
      show-cancel-button
      @confirm="handleSubmitExam"
      @cancel="showSubmitModal = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { QuestionForDisplay } from '@/src/types/api';

interface Props {
  attemptId?: string;
  questions: QuestionForDisplay[];
  currentQuestionIndex: number;
  selectedAnswers: string[];
  allAnswers: Record<number, string[]>;
  remainingTime: number;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: 'select-option', optionKey: string): void;
  (e: 'prev-question'): void;
  (e: 'next-question'): void;
  (e: 'submit-exam'): void;
  (e: 'essay-input', value: string): void;
}>();

const showSubmitModal = ref(false);
const essayAnswer = ref('');

const currentQuestion = computed(() => props.questions[props.currentQuestionIndex]);
const progressPercent = computed(() => 
  props.questions.length > 0 ? ((props.currentQuestionIndex + 1) / props.questions.length) * 100 : 0,
);

const submitModalContent = computed(() => {
  const unansweredCount = props.questions.length - Object.keys(props.allAnswers).length;
  if (unansweredCount > 0) {
    return `还有${unansweredCount}题未作答，确定要提交吗？`;
  }
  return '确定要提交试卷吗？提交后不可修改。';
});

function handleSelectOption(optionKey: string) {
  if (!currentQuestion.value) {
    console.warn('当前题目不存在，无法选择答案');
    return;
  }

  const questionType = currentQuestion.value.type;
  console.log('选择答案:', { questionType, optionKey, currentAnswers: props.selectedAnswers });

  emit('select-option', optionKey);
}

function handlePrevQuestion() {
  emit('prev-question');
}

function handleNextQuestion() {
  emit('next-question');
}

function handleShowSubmitConfirm() {
  showSubmitModal.value = true;
}

function handleSubmitExam() {
  showSubmitModal.value = false;
  emit('submit-exam');
}

function previewImage(imageUrl: string) {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl,
  });
}

function getQuestionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    judgment: '判断题',
    essay: '问答题',
    // 兼容旧版本类型
    single: '单选题',
    multiple: '多选题',
    judge: '判断题',
  };
  return typeMap[type] || '未知题型';
}

function getOptionText(option: any) {
  // 处理判断题特殊情况
  if (typeof option === 'object' && option.value) {
    if (option.value === 'true' || option.value === 'True') {
      return '正确';
    } else if (option.value === 'false' || option.value === 'False') {
      return '错误';
    }
    return option.value;
  }
  
  // 处理字符串选项
  if (typeof option === 'string') {
    if (option === 'true' || option === 'True') {
      return '正确';
    } else if (option === 'false' || option.value === 'False') {
      return '错误';
    }
    return option;
  }
  
  return option?.value || option || '';
}

function handleEssayInput(value: string) {
  essayAnswer.value = value;
  emit('essay-input', value);
}

function formatTime(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

.exam-execution-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: $background-color;
  overflow: hidden;

  .exam-header-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8rpx 16rpx;
    min-height: 44rpx;
    background: linear-gradient(135deg, $surface-color 0%, #f8faff 100%);
    border-bottom: 1rpx solid $border-color;
    box-shadow: $shadow-light;
    
    // 响应式适配
    @media (max-width: 320px) {
      padding: 6rpx 12rpx;
      min-height: 40rpx;
    }

    .exam-progress-section {
      flex: 1;
      margin-right: 16rpx;

      .progress-bar {
        height: 3rpx;
        background: rgba(64, 158, 255, 0.1);
        border-radius: 2rpx;
        overflow: hidden;
        margin-top: 2rpx;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, $primary-color, $primary-light);
          border-radius: 2rpx;
          transition: $transition-normal;
        }
      }
    }

    .exam-timer {
      display: flex;
      align-items: center;
      padding: 6rpx 12rpx;
      background: rgba(255, 107, 53, 0.1);
      border-radius: 12rpx;
      border: 1rpx solid rgba(255, 107, 53, 0.2);
    }
  }

  .exam-content-scroll {
    flex: 1;
    margin-top: 50rpx; // 为固定头部预留空间
    margin-bottom: 160rpx; // 为新的多行监控提醒预留更多空间
    padding: 0 12rpx;

    // 响应式适配
    @media (max-width: 320px) {
      margin-top: 46rpx;
      margin-bottom: 150rpx; // 小屏幕也增加底部空间
      padding: 0 8rpx;
    }

    .question-container {
      padding: 8rpx 0;

              .question-content {
          padding: 12rpx;

          .question-header {
            display: flex;
            align-items: center;
            margin-bottom: 8rpx;
          }

          .question-text {
            margin-bottom: 8rpx;
            word-wrap: break-word;
            word-break: break-all;
            white-space: pre-wrap;
            line-height: 1.6;
            overflow-wrap: break-word; // 增强换行支持
            hyphens: auto; // 自动断字

            // 确保u-text组件内的文本也能正确换行
            :deep(.u-text__value) {
              word-wrap: break-word !important;
              word-break: break-all !important;
              white-space: pre-wrap !important;
              overflow-wrap: break-word !important;
              display: block !important;
              width: 100% !important;
            }
          }

          .options-list {
            margin-top: 8rpx;
          
                      .essay-input-container {
              background: #f8faff;
              border-radius: 8rpx;
              padding: 12rpx;
              border: 1rpx solid #e4e7ed;
              
              .char-count {
                display: flex;
                justify-content: flex-end;
                margin-top: 6rpx;
              }
            }
            
                        .option-item {
              display: flex;
              align-items: flex-start;
              background: #f8faff;
              border: 2rpx solid transparent;
              border-radius: 8rpx;
              padding: 10rpx;
              margin-bottom: 6rpx;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              cursor: pointer;
              min-height: 44rpx; // 确保足够的触摸区域
              
              // 响应式适配
              @media (max-width: 320px) {
                padding: 8rpx;
                min-height: 40rpx;
              }

            &:hover {
              background: #ecf5ff;
              transform: translateY(-1rpx);
              box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.12);
            }

            &.selected {
              border-color: #409eff;
              background: linear-gradient(135deg, #ecf5ff 0%, #e1f3fe 100%);
              box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.15);

              .option-key {
                background: linear-gradient(135deg, #409eff, #66b1ff);
                color: white;
                box-shadow: 0 2rpx 6rpx rgba(64, 158, 255, 0.25);
              }
            }

            .option-key {
              width: 40rpx;
              height: 40rpx;
              border-radius: 50%;
              background: #ffffff;
              border: 2rpx solid #e4e7ed;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 22rpx;
              font-weight: 600;
              color: #606266;
              margin-right: 12rpx;
              flex-shrink: 0;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .option-text {
              flex: 1;
              padding-top: 2rpx;
              word-wrap: break-word;
              word-break: break-all;
              white-space: pre-wrap;
              line-height: 1.5;
              overflow-wrap: break-word; // 增强换行支持
              hyphens: auto; // 自动断字

              // 确保u-text组件内的文本也能正确换行
              :deep(.u-text__value) {
                word-wrap: break-word !important;
                word-break: break-all !important;
                white-space: pre-wrap !important;
                overflow-wrap: break-word !important;
                display: block !important;
                width: 100% !important;
              }
            }
          }
        }
      }
    }
  }

  .exam-footer-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10rpx 12rpx;
    background: $surface-color;
    border-top: 1rpx solid $border-color;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.06);
    z-index: 1000;
    
    // 响应式适配
    @media (max-width: 320px) {
      padding: 8rpx 10rpx;
    }

    .answer-actions {
      display: flex;
      gap: 10rpx;
      margin-bottom: 8rpx;
    }

    // 新的多行考试监控提醒样式
    .exam-warning-multiline {
      padding: 12rpx 16rpx;
      background: linear-gradient(135deg, #fffbe6 0%, #fdf8e0 100%);
      border: 1rpx solid rgba(230, 162, 60, 0.3);
      border-radius: 8rpx;
      box-shadow: 0 2rpx 8rpx rgba(230, 162, 60, 0.12);
      min-height: 60rpx; // 确保有足够的垂直空间

      .warning-header {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        // 响应式适配
        @media (max-width: 320px) {
          margin-bottom: 6rpx;
        }
      }

      .warning-text {
        padding-left: 22rpx; // 与图标对齐
        word-wrap: break-word;
        word-break: break-all;
        white-space: pre-wrap;
        line-height: 1.5;

        // 响应式适配
        @media (max-width: 320px) {
          padding-left: 20rpx;
          font-size: 20rpx;
        }
      }
    }

    // 保留旧样式以防兼容性问题
    .exam-warning-compact {
      padding: 6rpx 10rpx;
      background: linear-gradient(135deg, #fffbe6 0%, #fdf8e0 100%);
      border: 1rpx solid rgba(230, 162, 60, 0.3);
      border-radius: 6rpx;
      box-shadow: 0 2rpx 6rpx rgba(230, 162, 60, 0.1);

      .warning-content {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0;
        word-wrap: break-word;
        word-break: break-all;
        white-space: pre-wrap;
        line-height: 1.4;
      }
    }
  }
}
</style>