/**
 * 考试中心相关API接口
 * 更新时间：2025-06-22T00:38:48
 * 修复：API路径与cdcopenapi0620-2.yaml规范对齐
 */
import http from '../../utils/request';
import type { 
  Exam, 
  ExamDetail, 
  ExamAnswer, 
  FaceVerifyResult, 
  PageData, 
  PageParams,
  QuestionForDisplay,
  ExamItem,
  ExamVenue,
  OfflineBooking,
  ExamAttempt,
} from '../../types/api';

/**
 * 获取当前考试列表
 */
export function getCurrentExams() {
  return http.get<ExamItem[]>('/exams/current');
}

/**
 * 获取考试详情
 */
export function getExamDetail(examId: string) {
  return http.get<ExamDetail>(`/exams/${examId}`);
}

/**
 * 获取线上考试考前须知
 * @param examId 考试ID
 */
export function getOnlineExamRules(examId: string) {
  return http.get<{ rules: string }>(`/exams/online/${examId}/rules`);
}

/**
 * 开始线上考试尝试 (含人脸识别)
 * @param examId 考试ID
 * @param faceImage 人脸识别照片文件 (小程序环境下包含tempFilePath的对象)
 */
export function startOnlineExamAttempt(examId: string, faceImage: { tempFilePath: string; type: string; name: string }) {
  return http.upload<{ attemptId: string; questions: QuestionForDisplay[] }>(`/exams/online/${examId}/attempts`, {
    data: { faceImage },
  });
}

/**
 * 提交线上考试答案
 * @param attemptId 考试尝试ID
 * @param answers 答案数组
 */
export function submitOnlineExamAnswers(attemptId: string, answers: ExamAnswer[]) {
  return http.post<boolean>(`/exams/online/attempts/${attemptId}/answers`, { answers });
}

/**
 * 上报防作弊日志
 * @param attemptId 考试尝试ID
 * @param logs 防作弊事件日志
 * @param files 相关文件(如人脸抓拍照片)
 */
export function reportAnticheatLogs(
  attemptId: string, 
  logs: Array<{ type: string; timestamp: string; data?: Record<string, unknown> }>, 
  files?: { [key: string]: File },
) {
  const formData = {
    logs: JSON.stringify(logs),
    ...files,
  };
  return http.upload<void>(`/exams/online/attempts/${attemptId}/anticheat-logs`, { data: formData });
}

/**
 * 人脸识别验证 (兼容旧接口名称)
 * @deprecated 请使用 startOnlineExamAttempt
 */
export function verifyFaceIdentity(examId: string, imageData: string) {
  // 注意：这里需要将base64转换为File对象
  const file = new File([imageData], 'face.jpg', { type: 'image/jpeg' });
  return startOnlineExamAttempt(examId, file);
}

/**
 * 获取考试题目 (兼容旧接口名称)
 * @deprecated 考试题目现在通过 startOnlineExamAttempt 获取
 */
export function getExamQuestions(examId: string) {
  return http.get<QuestionForDisplay[]>(`/exams/${examId}/questions`);
}

/**
 * 提交考试答案 (兼容旧接口名称)
 * @deprecated 请使用 submitOnlineExamAnswers
 */
export function submitExamAnswers(examId: string, answers: ExamAnswer[]) {
  // 这里需要attemptId，暂时保持兼容
  return http.post<boolean>(`/exams/${examId}/submit`, { answers });
}

/**
 * 获取历史考试记录
 */
export function getExamHistory(page = 1, pageSize = 10) {
  return http.get<PageData<ExamAttempt>>('/exams/history', { 
    data: { page, pageSize }, 
  });
}

/**
 * 获取线下考试考场和场次信息
 * @param examId 考试ID
 */
export function getOfflineExamVenues(examId: string) {
  return http.get<ExamVenue[]>(`/exams/offline/${examId}/venues-schedules`);
}

/**
 * 预约线下考试场次
 * @param examId 考试ID
 * @param venueId 考场ID
 * @param scheduleId 场次ID
 */
export function bookOfflineExam(examId: string, venueId: string, scheduleId: string) {
  return http.post<OfflineBooking>(`/exams/offline/${examId}/book`, { venueId, scheduleId });
}

/**
 * 取消线下考试预约
 * @param examId 考试ID
 */
export function cancelOfflineExamBooking(examId: string) {
  return http.post<boolean>(`/exams/offline/${examId}/cancel-booking`);
}

/**
 * 报名线下考试 (兼容旧接口名称)
 * @deprecated 请使用 bookOfflineExam
 */
export function registerExam(examId: string) {
  return http.post<OfflineBooking>(`/exams/offline/${examId}/register`);
}

/**
 * 取消线下考试报名 (兼容旧接口名称)
 * @deprecated 请使用 cancelOfflineExamBooking
 */
export function cancelExamRegistration(examId: string) {
  return cancelOfflineExamBooking(examId);
}

/**
 * 获取线下考试报名信息 (兼容旧接口名称)
 * @deprecated 请使用 getOfflineExamVenues
 */
export function getRegistrationInfo(examId: string) {
  return getOfflineExamVenues(examId);
}
