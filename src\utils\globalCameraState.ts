/**
 * 全局摄像头状态管理
 * 确保整个应用中只有一个摄像头组件实例
 */

import { ref } from 'vue';

// 全局摄像头状态
export const globalCameraState = {
  // 当前活跃的摄像头组件ID
  activeCameraId: ref<string | null>(null),
  // 摄像头是否正在使用中
  isInUse: ref(false),
  // 摄像头初始化状态
  isInitialized: ref(false)
};

/**
 * 注册摄像头组件
 */
export function registerCamera(cameraId: string): boolean {
  if (globalCameraState.isInUse.value && globalCameraState.activeCameraId.value !== cameraId) {
    console.warn(`摄像头正在被使用中: ${globalCameraState.activeCameraId.value}, 无法注册新的摄像头: ${cameraId}`);
    return false;
  }
  
  globalCameraState.activeCameraId.value = cameraId;
  globalCameraState.isInUse.value = true;
  console.log(`摄像头组件已注册: ${cameraId}`);
  return true;
}

/**
 * 注销摄像头组件
 */
export function unregisterCamera(cameraId: string): void {
  if (globalCameraState.activeCameraId.value === cameraId) {
    globalCameraState.activeCameraId.value = null;
    globalCameraState.isInUse.value = false;
    globalCameraState.isInitialized.value = false;
    console.log(`摄像头组件已注销: ${cameraId}`);
  }
}

/**
 * 检查摄像头是否可用
 */
export function isCameraAvailable(cameraId: string): boolean {
  return !globalCameraState.isInUse.value || globalCameraState.activeCameraId.value === cameraId;
}

/**
 * 强制清理所有摄像头状态
 */
export function forceCleanupCamera(): void {
  globalCameraState.activeCameraId.value = null;
  globalCameraState.isInUse.value = false;
  globalCameraState.isInitialized.value = false;
  console.log('强制清理所有摄像头状态');
}

/**
 * 设置摄像头初始化状态
 */
export function setCameraInitialized(initialized: boolean): void {
  globalCameraState.isInitialized.value = initialized;
}
